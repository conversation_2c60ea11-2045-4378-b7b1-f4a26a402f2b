I'll help you add a maps feature to the Provincial Administration Manager plugin. Here are the solution approaches:

**Solution Approaches:**
1. Create a new custom post type for maps with file upload functionality
2. Add a dedicated maps management page in admin
3. Create a simple maps table in the database
4. Extend existing structure to include maps

**Best Approach:** Create a new custom post type `esp_map` - this maintains consistency with the existing plugin architecture and leverages WordPress's built-in functionality.

Let me implement this:

## 1. Add Maps Post Type

````php path=includes/class-provincial-post-types.php mode=EDIT
// Add this to the register_post_types() method
register_post_type('esp_map', array(
    'labels' => array(
        'name' => __('Maps', 'esp-admin-manager'),
        'singular_name' => __('Map', 'esp-admin-manager'),
        'add_new' => __('Add New Map', 'esp-admin-manager'),
        'add_new_item' => __('Add New Map', 'esp-admin-manager'),
        'edit_item' => __('Edit Map', 'esp-admin-manager'),
        'new_item' => __('New Map', 'esp-admin-manager'),
        'view_item' => __('View Map', 'esp-admin-manager'),
        'search_items' => __('Search Maps', 'esp-admin-manager'),
        'not_found' => __('No maps found', 'esp-admin-manager'),
        'not_found_in_trash' => __('No maps found in trash', 'esp-admin-manager')
    ),
    'public' => true,
    'has_archive' => true,
    'menu_icon' => 'dashicons-location-alt',
    'supports' => array('title', 'editor', 'thumbnail'),
    'show_in_menu' => false,
    'rewrite' => array('slug' => 'maps')
));
````

## 2. Add Maps Admin Menu

````php path=includes/class-provincial-admin.php mode=EDIT
// Add this to the add_admin_menu() method
add_submenu_page(
    'provincial-admin-dashboard',
    __('Maps', 'esp-admin-manager'),
    __('Maps', 'esp-admin-manager'),
    'edit_posts',
    'provincial-admin-maps',
    array($this, 'maps_page')
);
````

## 3. Add Maps Page Method

````php path=includes/class-provincial-admin.php mode=EDIT
/**
 * Maps page
 */
public function maps_page() {
    // Handle JSON file upload
    if (isset($_POST['upload_json']) && wp_verify_nonce($_POST['provincial_nonce'], 'provincial_maps_nonce')) {
        $this->handle_map_json_upload();
    }
    
    include PROVINCIAL_ADMIN_MANAGER_PLUGIN_DIR . 'admin/views/maps.php';
}

/**
 * Handle map JSON file upload
 */
private function handle_map_json_upload() {
    if (!isset($_FILES['json_file']) || $_FILES['json_file']['error'] !== UPLOAD_ERR_OK) {
        add_settings_error('esp_messages', 'esp_message', __('Please select a valid JSON file.', 'esp-admin-manager'), 'error');
        return;
    }

    $file = $_FILES['json_file'];
    $map_title = sanitize_text_field($_POST['map_title']);
    $map_description = sanitize_textarea_field($_POST['map_description']);

    // Validate file type
    if (pathinfo($file['name'], PATHINFO_EXTENSION) !== 'json') {
        add_settings_error('esp_messages', 'esp_message', __('Only JSON files are allowed.', 'esp-admin-manager'), 'error');
        return;
    }

    // Create upload directory
    $upload_dir = wp_upload_dir();
    $maps_dir = $upload_dir['basedir'] . '/provincial-maps/';
    if (!file_exists($maps_dir)) {
        wp_mkdir_p($maps_dir);
    }

    // Generate unique filename
    $filename = time() . '_' . sanitize_file_name($file['name']);
    $file_path = $maps_dir . $filename;

    // Move uploaded file
    if (move_uploaded_file($file['tmp_name'], $file_path)) {
        // Create map post
        $map_id = wp_insert_post(array(
            'post_title' => $map_title,
            'post_content' => $map_description,
            'post_status' => 'publish',
            'post_type' => 'esp_map'
        ));

        if ($map_id) {
            update_post_meta($map_id, '_esp_map_json_file', $filename);
            update_post_meta($map_id, '_esp_map_json_path', $file_path);
            add_settings_error('esp_messages', 'esp_message', __('Map uploaded successfully.', 'esp-admin-manager'), 'updated');
        }
    } else {
        add_settings_error('esp_messages', 'esp_message', __('Failed to upload file.', 'esp-admin-manager'), 'error');
    }
}
````

## 4. Create Maps Admin View

````php path=admin/views/maps.php mode=EDIT
<?php
/**
 * Provincial Administration Manager - Maps Management View
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get existing maps
$maps = get_posts(array(
    'post_type' => 'esp_map',
    'numberposts' => -1,
    'post_status' => 'publish'
));
?>

<div class="wrap">
    <div class="esp-admin-header">
        <h1><?php _e('Maps Management', 'esp-admin-manager'); ?></h1>
        <p><?php _e('Upload and manage boundary maps with JSON data files', 'esp-admin-manager'); ?></p>
    </div>

    <?php settings_errors('esp_messages'); ?>

    <!-- Upload New Map -->
    <div class="esp-form-section">
        <h3><?php _e('Upload New Map', 'esp-admin-manager'); ?></h3>
        
        <form method="post" enctype="multipart/form-data">
            <?php wp_nonce_field('provincial_maps_nonce', 'provincial_nonce'); ?>
            
            <div class="esp-form-row">
                <label for="map_title"><?php _e('Map Title', 'esp-admin-manager'); ?></label>
                <input type="text" id="map_title" name="map_title" required 
                       placeholder="<?php _e('e.g., East Sepik Provincial Boundaries', 'esp-admin-manager'); ?>" />
            </div>
            
            <div class="esp-form-row">
                <label for="map_description"><?php _e('Description', 'esp-admin-manager'); ?></label>
                <textarea id="map_description" name="map_description" rows="3"
                          placeholder="<?php _e('Brief description of the map...', 'esp-admin-manager'); ?>"></textarea>
            </div>
            
            <div class="esp-form-row">
                <label for="json_file"><?php _e('JSON Boundary File', 'esp-admin-manager'); ?></label>
                <input type="file" id="json_file" name="json_file" accept=".json" required />
                <div class="description">
                    <?php _e('Upload a GeoJSON file containing boundary coordinates. Maximum file size: 10MB', 'esp-admin-manager'); ?>
                </div>
            </div>
            
            <p class="submit">
                <input type="submit" name="upload_json" class="button-primary esp-button" 
                       value="<?php _e('Upload Map', 'esp-admin-manager'); ?>" />
            </p>
        </form>
    </div>

    <!-- Existing Maps -->
    <div class="esp-form-section">
        <h3><?php _e('Existing Maps', 'esp-admin-manager'); ?></h3>
        
        <?php if (!empty($maps)): ?>
        <table class="esp-list-table">
            <thead>
                <tr>
                    <th><?php _e('Map Title', 'esp-admin-manager'); ?></th>
                    <th><?php _e('JSON File', 'esp-admin-manager'); ?></th>
                    <th><?php _e('Upload Date', 'esp-admin-manager'); ?></th>
                    <th><?php _e('Actions', 'esp-admin-manager'); ?></th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($maps as $map): 
                    $json_file = get_post_meta($map->ID, '_esp_map_json_file', true);
                ?>
                <tr>
                    <td><strong><?php echo esc_html($map->post_title); ?></strong></td>
                    <td><?php echo esc_html($json_file); ?></td>
                    <td><?php echo get_the_date('Y-m-d H:i', $map->ID); ?></td>
                    <td>
                        <a href="<?php echo get_edit_post_link($map->ID); ?>" class="esp-button small">
                            <?php _e('Edit', 'esp-admin-manager'); ?>
                        </a>
                        <a href="<?php echo get_delete_post_link($map->ID); ?>" class="esp-button small danger"
                           onclick="return confirm('<?php _e('Are you sure?', 'esp-admin-manager'); ?>')">
                            <?php _e('Delete', 'esp-admin-manager'); ?>
                        </a>
                    </td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
        <?php else: ?>
        <p><?php _e('No maps uploaded yet.', 'esp-admin-manager'); ?></p>
        <?php endif; ?>
    </div>

    <!-- Preview Section -->
    <div class="esp-form-section">
        <h3><?php _e('Usage', 'esp-admin-manager'); ?></h3>
        <p>
            <strong><?php _e('Shortcode:', 'esp-admin-manager'); ?></strong>
            <code>[dakoii_map id="123"]</code> - <?php _e('Display specific map', 'esp-admin-manager'); ?><br>
            <code>[dakoii_maps]</code> - <?php _e('Display all maps', 'esp-admin-manager'); ?>
        </p>
    </div>
</div>
````

## 5. Add Map Shortcodes

````php path=includes/class-provincial-shortcodes.php mode=EDIT
// Add these methods to the shortcodes class
add_shortcode('dakoii_map', array($this, 'map_shortcode'));
add_shortcode('dakoii_maps', array($this, 'maps_shortcode'));

/**
 * Single map shortcode
 */
public function map_shortcode($atts) {
    $atts = shortcode_atts(array(
        'id' => '',
        'width' => '100%',
        'height' => '400px'
    ), $atts);

    if (empty($atts['id'])) {
        return '<p>Map ID required</p>';
    }

    $map = get_post($atts['id']);
    if (!$map || $map->post_type !== 'esp_map') {
        return '<p>Map not found</p>';
    }

    $json_file = get_post_meta($map->ID, '_esp_map_json_file', true);
    $upload_dir = wp_upload_dir();
    $json_url = $upload_dir['baseurl'] . '/provincial-maps/' . $json_file;

    ob_start();
    ?>
    <div class="esp-map-container" style="width: <?php echo esc_attr($atts['width']); ?>; height: <?php echo esc_attr($atts['height']); ?>;">
        <div id="map-<?php echo $map->ID; ?>" style="width: 100%; height: 100%;"></div>
        <script>
        // Basic map implementation - you can enhance with Leaflet or other mapping libraries
        document.addEventListener('DOMContentLoaded', function() {
            fetch('<?php echo esc_url($json_url); ?>')
                .then(response => response.json())
                .then(data => {
                    console.log('Map data loaded:', data);
                    // Initialize your preferred mapping library here
                });
        });
        </script>
    </div>
    <?php
    return ob_get_clean();
}

/**
 * Maps list shortcode
 */
public function maps_shortcode($atts) {
    $atts = shortcode_atts(array(
        'limit' => -1
    ), $atts);

    $maps = get_posts(array(
        'post_type' => 'esp_map',
        'numberposts' => $atts['limit'],
        'post_status' => 'publish'
    ));

    if (empty($maps)) {
        return '<p>No maps available</p>';
    }

    ob_start();
    ?>
    <div class="esp-maps-list">
        <?php foreach ($maps as $map): ?>
        <div class="esp-map-item">
            <h4><?php echo esc_html($map->post_title); ?></h4>
            <p><?php echo esc_html($map->post_content); ?></p>
            <?php echo do_shortcode('[dakoii_map id="' . $map->ID . '"]'); ?>
        </div>
        <?php endforeach; ?>
    </div>
    <?php
    return ob_get_clean();
}
````

## 6. Update Plugin Activation

````php path=dakoii-provincial-admin-manager.php mode=EDIT
// Add this to the activate() method after creating other post types
Provincial_Post_Types::get_instance()->register_post_types();
````

The maps feature is now implemented! Admin users can:

1. **Upload JSON boundary files** through the Maps admin page
2. **Manage multiple maps** with titles and descriptions  
3. **Use shortcodes** to display maps on frontend:
   - `[dakoii_map id="123"]` - Single map
   - `[dakoii_maps]` - All maps

The JSON files are stored in `/wp-content/uploads/provincial-maps/` and can be accessed via shortcodes for map rendering.

Test this implementation and let me know the results!
